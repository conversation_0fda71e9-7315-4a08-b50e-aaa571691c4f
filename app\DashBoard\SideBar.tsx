"use client";
import { Fragment, useEffect, useState, useRef } from "react";
import { Dialog, Transition } from "@headlessui/react";
import {
  Bars3Icon,
  Cog6ToothIcon,
  ClockIcon,
  XMarkIcon,
  BanknotesIcon,
  ChartBarIcon,
  ShoppingCartIcon,
  CurrencyDollarIcon,
  CalculatorIcon,
  HomeIcon,
  DocumentIcon,
  ArrowTrendingUpIcon,
  CircleStackIcon,
  ChartPieIcon,
  BuildingOffice2Icon,
  ChevronDownIcon,
  UserGroupIcon,
  ArrowPathIcon,
  UsersIcon,
  NewspaperIcon,
  DocumentChartBarIcon,
} from "@heroicons/react/24/outline";
import { onAuthStateChanged, signOut, User, updateProfile, updateEmail } from "firebase/auth";
import { auth, db, storage } from "../../firebase";
import { doc, getDoc, updateDoc, collection, query, where, orderBy, limit, getDocs } from "firebase/firestore";
import { listAll, ref as storageRef, uploadBytes, getDownloadURL } from "firebase/storage";
import { useRouter, usePathname } from "next/navigation";
import { getUserData, getEffectiveUserId, fetchUserClients } from "../../utils/accountUtils";
// Import Chart.js components
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  Filler,
} from 'chart.js';
import { Line, Doughnut } from 'react-chartjs-2';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  Filler
);

// Add interface for dropdown items
interface DropdownItem {
  name: string;
  href: string;
}

// Add interface for navigation item
interface NavigationItem {
  name: string;
  href: string;
  icon: any;
  current: boolean;
  iconColor: string;
  dropdownItems?: DropdownItem[];
  bankItems?: { name: string; href: string }[];
}

// Add interface for navigation group
interface NavigationGroup {
  category: string;
  items: NavigationItem[];
}

// Add this under the navigation interface declarations
interface ClientIndicatorProps {
  clientName: string;
}

// Add this interface for recent invoices
interface RecentInvoice {
  id: string;
  invoiceNumber: string;
  type: string;
  date: string;
  amount: number;
  status: 'Paid' | 'Pending' | 'Overdue';
  clientId: string;
  clientName: string;
}

// Update the icon styling in getNavigation function
const getNavigation = (currentPath: string): NavigationGroup[] => [
  {
    category: "Main",
    items: [
      {
        name: "Home",
        href: "/DashBoard",
        icon: HomeIcon,
        current: currentPath === "/DashBoard",
        iconColor: "text-emerald-400"
      },
    ]
  },
  {
    category: "Invoices",
    items: [
      {
        name: "Buy Invoice",
        href: "/Invoices/Achat",
        icon: ShoppingCartIcon,
        current: currentPath === "/Invoices/Achat",
        iconColor: "text-purple-400"
      },
      {
        name: "Sell Invoice",
        href: "/Invoices/Vente",
        icon: CurrencyDollarIcon,
        current: currentPath === "/Invoices/Vente",
        iconColor: "text-blue-400"
      },
      {
        name: "Register Invoice",
        href: "/Invoices/Caisse",
        icon: CalculatorIcon,
        current: currentPath === "/Invoices/Caisse",
        iconColor: "text-orange-400"
      },
      {
        name: "Various Operations",
        href: "/Invoices/Od",
        icon: BuildingOffice2Icon,
        current: currentPath === "/Invoices/Od",
        iconColor: "text-green-400"
      },
      {
        name: "Bank Statements",
        href: "/Invoices/Banks",
        icon: BanknotesIcon,
        current: currentPath.startsWith("/Invoices/Banks"),
        iconColor: "text-green-400",
      },
            {
        name: "Monthly Declaration",
        href: "/MonthlyDeclaration",
        icon: DocumentChartBarIcon,
        current: currentPath === "/MonthlyDeclaration",
        iconColor: "text-teal-400"
      },
                  {
        name: "Pay Slips",
        href: "/PaySlips",
        icon: NewspaperIcon,
        current: currentPath === "/PaySlips",
        iconColor: "text-teal-400"
      },
      {
        name: "Invoices History",
        href: "/Invoices/History",
        icon: ClockIcon,
        current: currentPath === "/Invoices/History",
        iconColor: "text-red-400"
      },
    ]
  },
  {
    category: "Settings",
    items: [
      {
        name: "Settings",
        href: "/ClientList",
        icon: Cog6ToothIcon,
        current: currentPath === "/ClientList",
        iconColor: "text-yellow-400"
      },
      {
        name: "Sub-Accounts",
        href: "/SubAccounts",
        icon: UsersIcon,
        current: currentPath === "/SubAccounts",
        iconColor: "text-indigo-400"
      },
    ]
  },
];

function classNames(...classes: string[]) {
  return classes.filter(Boolean).join(" ");
}

interface InvoiceData {
  type: 'Achat' | 'Vente' | 'Caisse' | 'Banks';
  month: number;
  amount?: number;
}

export default function SideBar() {
  const pathname = usePathname();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [userData, setUserData] = useState<{
    name: string;
    profileImageUrl: string;
    isSubAccount?: boolean;
  } | null>(null);
  const [totalInvoices, setTotalInvoices] = useState<number>(0);
  const [chartData, setChartData] = useState({
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
    datasets: {
      sales: {
        label: 'Sales Invoices',
        data: [12, 19, 3, 5, 2, 3, 8, 14, 7, 10, 13, 9],
        borderColor: 'rgb(75, 192, 192)',
        backgroundColor: 'rgba(75, 192, 192, 0.5)',
        tension: 0.4,
      },
      purchases: {
        label: 'Purchase Invoices',
        data: [8, 15, 5, 7, 3, 2, 9, 6, 11, 13, 8, 5],
        borderColor: 'rgb(255, 99, 132)',
        backgroundColor: 'rgba(255, 99, 132, 0.5)',
        tension: 0.4,
      },
      banking: {
        label: 'Banking Transactions',
        data: [5, 10, 8, 4, 6, 9, 12, 7, 3, 8, 10, 15],
        borderColor: 'rgb(53, 162, 235)',
        backgroundColor: 'rgba(53, 162, 235, 0.5)',
        tension: 0.4,
      },
      caisse: {
        label: 'Register Transactions',
        data: [3, 7, 4, 8, 9, 5, 6, 8, 9, 7, 5, 11],
        borderColor: 'rgb(255, 159, 64)',
        backgroundColor: 'rgba(255, 159, 64, 0.5)',
        tension: 0.4,
      }
    },
    doughnut: {
      labels: ['Sales', 'Purchases', 'Register', 'Banking'],
      datasets: [{
        data: [300, 200, 150, 100],
        backgroundColor: [
          'rgba(75, 192, 192, 0.7)',
          'rgba(255, 99, 132, 0.7)',
          'rgba(255, 159, 64, 0.7)',
          'rgba(53, 162, 235, 0.7)',
        ],
        borderColor: [
          'rgb(75, 192, 192)',
          'rgb(255, 99, 132)',
          'rgb(255, 159, 64)',
          'rgb(53, 162, 235)',
        ],
        borderWidth: 1,
        hoverOffset: 4,
      }],
    }
  });
  const router = useRouter();
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [newEmail, setNewEmail] = useState("");
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [openDropdown, setOpenDropdown] = useState<string | null>(null);
  const [currentClient, setCurrentClient] = useState<string>("");
  const [openBankMenu, setOpenBankMenu] = useState(false);
  const [bankSearchQuery, setBankSearchQuery] = useState("");
  const [recentInvoices, setRecentInvoices] = useState<RecentInvoice[]>([]);
  const [loadingInvoices, setLoadingInvoices] = useState(false);
  const [clients, setClients] = useState<Record<string, any>>({});

  const extractClientName = (path: string) => {
    return "Genius Global Business";
  };

  // Get navigation items based on user type
  const getFilteredNavigation = () => {
    const navItems = getNavigation(pathname);

    // If user is a sub-account, filter out the Sub-Accounts menu item
    if (userData?.isSubAccount) {
      return navItems.map(group => {
        if (group.category === "Settings") {
          return {
            ...group,
            items: group.items.filter(item => item.name !== "Sub-Accounts")
          };
        }
        return group;
      });
    }

    return navItems;
  };

  const navigation = getFilteredNavigation();

  const handleSignOut = async () => {
    try {
      if (auth.currentUser) {
        const userId = auth.currentUser.uid;
        const userDocRef = doc(db, "users", userId);
        await updateDoc(userDocRef, {
          'sessionInfo.isActive': false,
        });
        localStorage.removeItem('sessionId');
      }
      await signOut(auth);
      console.log("User signed out successfully.");
      router.push("/SignInScreen");
    } catch (error) {
      console.error("Error signing out:", error);
      router.push("/SignInScreen");
    }
  };

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (currentUser) => {
      if (currentUser) {
        try {
          // Use the utility function to get user data (handles both regular users and sub-accounts)
          const userDataResult = await getUserData(currentUser.uid);
          if (userDataResult) {
            setUserData(userDataResult);
          } else {
            console.log("No user data found!");
          }
        } catch (error) {
          console.error("Error fetching user data:", error);
        }
      } else {
        setUserData(null);
        router.push("/");
      }
    });

    return () => unsubscribe();
  }, [router]);

  const getMonthFromFileName = (fileName: string): number => {
    const dateMatch = fileName.match(/\d{8}/);
    if (dateMatch) {
      const monthStr = dateMatch[0].substring(4, 6);
      return parseInt(monthStr, 10);
    }
    return 0;
  };

  const getInvoiceType = (fileName: string): InvoiceData['type'] => {
    if (fileName.includes('Achat')) return 'Achat';
    if (fileName.includes('Vente')) return 'Vente';
    if (fileName.includes('Caisse')) return 'Caisse';
    if (fileName.includes('Banks')) return 'Banks';
    return 'Achat';
  };

  const processInvoiceData = (fileList: any[]) => {
    const monthLabels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const invoicesByType: Record<string, number[]> = {
      Achat: Array(12).fill(0),
      Vente: Array(12).fill(0),
      Caisse: Array(12).fill(0),
      Banks: Array(12).fill(0),
    };

    fileList.forEach(file => {
      const fileName = file.name;
      const month = getMonthFromFileName(fileName) - 1;
      const type = getInvoiceType(fileName);

      if (month >= 0 && month < 12) {
        invoicesByType[type][month]++;
      }
    });

    setChartData({
      labels: monthLabels,
      datasets: {
        sales: {
          label: 'Sales Invoices',
          data: invoicesByType['Vente'],
          borderColor: 'rgb(75, 192, 192)',
          backgroundColor: 'rgba(75, 192, 192, 0.5)',
          tension: 0.4,
        },
        purchases: {
          label: 'Purchase Invoices',
          data: invoicesByType['Achat'],
          borderColor: 'rgb(255, 99, 132)',
          backgroundColor: 'rgba(255, 99, 132, 0.5)',
          tension: 0.4,
        },
        banking: {
          label: 'Banking Transactions',
          data: invoicesByType['Banks'],
          borderColor: 'rgb(53, 162, 235)',
          backgroundColor: 'rgba(53, 162, 235, 0.5)',
          tension: 0.4,
        },
        caisse: {
          label: 'Register Transactions',
          data: invoicesByType['Caisse'],
          borderColor: 'rgb(255, 159, 64)',
          backgroundColor: 'rgba(255, 159, 64, 0.5)',
          tension: 0.4,
        }
      },
      doughnut: {
        labels: ['Sales', 'Purchases', 'Register', 'Banking'],
        datasets: [{
          data: [
            invoicesByType['Vente'].reduce((a, b) => a + b, 0),
            invoicesByType['Achat'].reduce((a, b) => a + b, 0),
            invoicesByType['Caisse'].reduce((a, b) => a + b, 0),
            invoicesByType['Banks'].reduce((a, b) => a + b, 0),
          ],
          backgroundColor: [
            'rgba(75, 192, 192, 0.5)',
            'rgba(255, 99, 132, 0.5)',
            'rgba(255, 159, 64, 0.5)',
            'rgba(53, 162, 235, 0.5)',
          ],
          borderColor: [
            'rgb(75, 192, 192)',
            'rgb(255, 99, 132)',
            'rgb(255, 159, 64)',
            'rgb(53, 162, 235)',
          ],
          borderWidth: 1,
          hoverOffset: 4,
        }],
      }
    });
  };

  useEffect(() => {
    const fetchInvoicesData = async () => {
      try {
        const invoicesRef = storageRef(storage, "invoices");
        const invoicesSnapshot = await listAll(invoicesRef);
        setTotalInvoices(invoicesSnapshot.items.length);
        processInvoiceData(invoicesSnapshot.items);
      } catch (error) {
        console.error("Error fetching invoices data:", error);
      }
    };

    fetchInvoicesData();
  }, []);

  const fetchClients = async () => {
    if (!auth.currentUser) return {};

    try {
      // Use the utility function to fetch clients (handles both regular users and sub-accounts)
      const clientsData = await fetchUserClients(auth.currentUser);
      setClients(clientsData);
      return clientsData;
    } catch (error) {
      console.error("Error fetching clients:", error);
      return {};
    }
  };

  const fetchRecentInvoices = async () => {
    if (!auth.currentUser) return;

    setLoadingInvoices(true);
    try {
      const clientsData = await fetchClients();

      // Get the effective user ID (parent ID for sub-accounts, own ID for regular users)
      const effectiveUserId = await getEffectiveUserId(auth.currentUser.uid);

      const invoicesQuery = query(
        collection(db, "invoices"),
        where("userId", "==", effectiveUserId),
        orderBy("uploadedAt", "desc"),
        limit(5)
      );

      const invoicesSnapshot = await getDocs(invoicesQuery);

      const invoices = invoicesSnapshot.docs.map(doc => {
        const data = doc.data();

        const client = clientsData[data.clientId];

        let type = "Unknown";
        if (data.entries && data.entries.length > 0 && client && client.sageCodes) {
          const codeFacture = data.entries[0]["Code Facture"];
          const { achatCode, venteCode, caisseCode, odCode } = client.sageCodes;

          if (codeFacture === achatCode) type = "Purchase";
          else if (codeFacture === venteCode) type = "Sale";
          else if (codeFacture === caisseCode) type = "Register";
          else if (codeFacture === odCode) type = "OD";
        }

        const uploadDate = new Date(data.uploadedAt);
        const now = new Date();
        const daysDiff = Math.floor((now.getTime() - uploadDate.getTime()) / (1000 * 3600 * 24));

        let status: 'Paid' | 'Pending' | 'Overdue' = 'Pending';
        if (type === "Sale") {
          status = daysDiff > 30 ? 'Overdue' : daysDiff > 15 ? 'Pending' : 'Paid';
        } else {
          status = daysDiff > 20 ? 'Paid' : 'Pending';
        }

        return {
          id: doc.id,
          invoiceNumber: data.invoiceNumber || `INV-${new Date(data.uploadedAt).toISOString().slice(0, 10)}`,
          type,
          date: data.invoiceDate || new Date(data.uploadedAt).toISOString().slice(0, 10),
          amount: data.totalAmount || 0,
          status,
          clientId: data.clientId,
          clientName: data.clientName || "Unknown Client"
        };
      });

      setRecentInvoices(invoices);
    } catch (error) {
      console.error("Error fetching recent invoices:", error);
    } finally {
      setLoadingInvoices(false);
    }
  };

  useEffect(() => {
    if (userData) {
      fetchRecentInvoices();
    }
  }, [userData]);

  const handleSettingsClick = (e: React.MouseEvent, href: string) => {
    if (href === "#") {
      e.preventDefault();
      setIsSettingsOpen(true);
    }
  };

  const handleProfileImageUpdate = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !auth.currentUser) return;

    try {
      const imageRef = storageRef(storage, `profileImages/${auth.currentUser.uid}`);
      await uploadBytes(imageRef, file);
      const downloadURL = await getDownloadURL(imageRef);

      await updateProfile(auth.currentUser, {
        photoURL: downloadURL,
      });

      const userDocRef = doc(db, "users", auth.currentUser.uid);
      await updateDoc(userDocRef, {
        profileImageUrl: downloadURL,
      });

      setUserData(prev => prev ? { ...prev, profileImageUrl: downloadURL } : null);
    } catch (error) {
      console.error("Error updating profile image:", error);
    }
  };

  const handleEmailUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!auth.currentUser) return;

    try {
      await updateEmail(auth.currentUser, newEmail);

      const userDocRef = doc(db, "users", auth.currentUser.uid);
      await updateDoc(userDocRef, {
        email: newEmail,
      });

      setNewEmail("");
      setIsSettingsOpen(false);
    } catch (error) {
      console.error("Error updating email:", error);
    }
  };

  const handleDropdownClick = (e: React.MouseEvent, itemName: string) => {
    e.preventDefault();
    setOpenDropdown(openDropdown === itemName ? null : itemName);
  };

  const handleBankClick = (e: React.MouseEvent, item: NavigationItem) => {
    e.preventDefault();
    if (item.bankItems) {
      setOpenBankMenu(!openBankMenu);
    }
  };

  const filterBanks = (banks: { name: string; href: string }[]) => {
    return banks.filter(bank =>
      bank.name.toLowerCase().includes(bankSearchQuery.toLowerCase())
    );
  };

  const lineOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      tooltip: {
        mode: 'index' as const,
        intersect: false,
      },
    },
    hover: {
      mode: 'nearest' as const,
      intersect: true,
    },
    scales: {
      y: {
        beginAtZero: true,
      },
    },
  };

  const doughnutOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'right' as const,
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            let label = context.label || '';
            if (label) {
              label += ': ';
            }
            const value = context.raw || 0;
            const total = context.chart.data.datasets[0].data.reduce((a: number, b: number) => a + b, 0);
            const percentage = Math.round((value / total) * 100);
            return `${label} ${value} (${percentage}%)`;
          }
        }
      }
    },
    cutout: '65%',
  };

  const monthlyActivityData = {
    labels: chartData.labels,
    datasets: [
      chartData.datasets.sales,
      chartData.datasets.purchases,
      chartData.datasets.banking,
      chartData.datasets.caisse,
    ],
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'Paid':
        return 'bg-green-100 text-green-800';
      case 'Pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'Overdue':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatAmount = (amount: number): string => {
    return amount.toFixed(3).replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  };

  const formatDate = (dateString: string): string => {
    try {
      const date = new Date(dateString);
      return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
    } catch (error) {
      return dateString;
    }
  };

  return (
    <div className="overflow-x-hidden">
      <Transition appear show={isSettingsOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={setIsSettingsOpen}>
          <div className="fixed inset-0 bg-black/25" />
          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                  <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-gray-900">
                    Settings
                  </Dialog.Title>

                  <div className="mt-4">
                    <div className="mb-6">
                      <label className="block text-sm font-medium text-gray-700">Profile Image</label>
                      <div className="mt-2 flex items-center space-x-4">
                        <img
                          src={userData?.profileImageUrl || "https://res.cloudinary.com/dfilgqymt/image/upload/f_auto,q_auto/360_F_215844325_ttX9YiIIyeaR7Ne6EaLLjMAmy4GvPC69_bf0abd"}
                          alt="Profile"
                          className="h-16 w-16 rounded-full object-cover"
                        />
                        <input
                          type="file"
                          ref={fileInputRef}
                          onChange={handleProfileImageUpdate}
                          accept="image/*"
                          className="hidden"
                        />
                        <button
                          type="button"
                          onClick={() => fileInputRef.current?.click()}
                          className="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                        >
                          Change Image
                        </button>
                      </div>
                    </div>

                    <form onSubmit={handleEmailUpdate} className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700">Email</label>
                        <input
                          type="email"
                          value={newEmail}
                          onChange={(e) => setNewEmail(e.target.value)}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                          placeholder="Enter new email"
                        />
                      </div>
                      <div className="flex justify-end space-x-3">
                        <button
                          type="button"
                          onClick={() => setIsSettingsOpen(false)}
                          className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
                        >
                          Cancel
                        </button>
                        <button
                          type="submit"
                          className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
                        >
                          Save Changes
                        </button>
                      </div>
                    </form>
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>

      <Transition.Root show={sidebarOpen} as={Fragment}>
        <Dialog as="div" className="relative z-50 lg:hidden" onClose={setSidebarOpen}>
          <Transition.Child
            as={Fragment}
            enter="transition-opacity ease-linear duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="transition-opacity ease-linear duration-300"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-gray-900/80" />
          </Transition.Child>

          <div className="fixed inset-0 flex">
            <Transition.Child
              as={Fragment}
              enter="transition ease-in-out duration-300 transform"
              enterFrom="-translate-x-full"
              enterTo="translate-x-0"
              leave="transition ease-in-out duration-300 transform"
              leaveFrom="translate-x-0"
              leaveTo="-translate-x-full"
            >
              <Dialog.Panel className="relative mr-16 flex w-full max-w-xs flex-1">
                <Transition.Child
                  enter="ease-in-out duration-300"
                  enterFrom="opacity-0"
                  enterTo="opacity-100"
                  leave="ease-in-out duration-300"
                  leaveFrom="opacity-100"
                  leaveTo="opacity-0"
                >
                  <div className="absolute left-full top-0 flex w-16 justify-center pt-5">
                    <button type="button" className="-m-2.5 p-2.5" onClick={() => setSidebarOpen(false)}>
                      <span className="sr-only">Close sidebar</span>
                      <XMarkIcon className="h-6 w-6 text-white" aria-hidden="true" />
                    </button>
                  </div>
                </Transition.Child>
                <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-gray-900 px-6 pb-2 ring-1 ring-white/10 scrollbar-hide">
                  <div className="flex h-16 shrink-0 items-center">
                    <span className="h-16 w-auto flex items-center justify-center text-white">
                      Genius Invoices
                    </span>
                  </div>
                  <nav className="flex flex-1 flex-col">
                    <ul role="list" className="flex flex-1 flex-col gap-y-7">
                      {navigation.map((group) => (
                        <li key={group.category}>
                          <div className="text-xs font-semibold leading-6 text-gray-400">{group.category}</div>
                          <ul role="list" className="-mx-2 mt-2 space-y-1">
                            {group.items.map((item) => (
                              <li key={item.name}>
                                <a
                                  href={item.href}
                                  onClick={(e) => {
                                    if (item.bankItems) {
                                      handleBankClick(e, item);
                                    } else if (item.dropdownItems) {
                                      handleDropdownClick(e, item.name);
                                    } else {
                                      handleSettingsClick(e, item.href);
                                    }
                                  }}
                                  className={classNames(
                                    item.current
                                      ? "bg-gray-800 text-white border-l-4 border-blue-500"
                                      : "text-gray-400 hover:text-white hover:bg-gray-800",
                                    "group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold transition-colors duration-200"
                                  )}
                                >
                                  <item.icon
                                    className={classNames(
                                      "h-6 w-6 shrink-0 transition-all duration-300 ease-in-out",
                                      item.current
                                        ? "text-white scale-110"
                                        : item.iconColor,
                                      "group-hover:scale-110 group-hover:rotate-3"
                                    )}
                                    aria-hidden="true"
                                  />
                                  <span className="flex-1">{item.name}</span>
                                  {(item.dropdownItems || item.bankItems) && (
                                    <ChevronDownIcon
                                      className={classNames(
                                        "h-5 w-5 transition-transform duration-200",
                                        (openDropdown === item.name || (item.bankItems && openBankMenu)) ? "rotate-180" : ""
                                      )}
                                    />
                                  )}
                                </a>
                                {item.bankItems && openBankMenu && (
                                  <div className="mt-1 px-8">
                                    <div className="mb-2">
                                      <input
                                        type="text"
                                        value={bankSearchQuery}
                                        onChange={(e) => setBankSearchQuery(e.target.value)}
                                        placeholder="Search banks..."
                                        className="w-full px-3 py-2 text-sm bg-gray-700 text-gray-200 rounded-md border border-gray-600 focus:outline-none focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                                      />
                                    </div>
                                    <ul className="space-y-1 max-h-48 overflow-y-auto scrollbar-hide" style={{ msOverflowStyle: 'none', scrollbarWidth: 'none' }}>
                                      {filterBanks(item.bankItems).map((bank) => (
                                        <li key={bank.name}>
                                          <a
                                            href={bank.href}
                                            className="block rounded-md py-2 px-3 text-sm text-gray-400 hover:bg-gray-800 hover:text-white transition-colors duration-200"
                                          >
                                            {bank.name}
                                          </a>
                                        </li>
                                      ))}
                                      {filterBanks(item.bankItems).length === 0 && (
                                        <li className="py-2 px-3 text-sm text-gray-500 italic">
                                          No banks found
                                        </li>
                                      )}
                                    </ul>
                                  </div>
                                )}
                                {item.dropdownItems && openDropdown === item.name && (
                                  <ul className="mt-1 space-y-1 px-8">
                                    {item.dropdownItems.map((dropdownItem) => (
                                      <li key={dropdownItem.name}>
                                        <a
                                          href={dropdownItem.href}
                                          className="block rounded-md py-2 px-3 text-sm text-gray-400 hover:bg-gray-800 hover:text-white transition-colors duration-200"
                                        >
                                          {dropdownItem.name}
                                        </a>
                                      </li>
                                    ))}
                                  </ul>
                                )}
                              </li>
                            ))}
                          </ul>
                        </li>
                      ))}

                      <li className="-mx-6 mt-auto">
                        <div className="flex items-center gap-x-4 px-6 py-3 text-sm font-semibold leading-6 text-white hover:bg-gray-800 transition-colors duration-200">
                          <div className="relative">
                            <img
                              className="h-10 w-10 rounded-full border-2 border-gray-600"
                              src={userData?.profileImageUrl || "https://res.cloudinary.com/dfilgqymt/image/upload/f_auto,q_auto/360_F_215844325_ttX9YiIIyeaR7Ne6EaLLjMAmy4GvPC69_bf0abd"}
                              alt={userData?.name || "User"}
                            />
                            {userData?.isSubAccount && (
                              <span className="absolute -top-1 -right-1 bg-indigo-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                                S
                              </span>
                            )}
                          </div>
                          <div className="flex-1">
                            <span>{userData?.name || "Anonymous User"}</span>
                            {userData?.isSubAccount && (
                              <span className="block text-xs text-gray-400">Sub-Account</span>
                            )}
                          </div>
                          <button
                            onClick={handleSignOut}
                            className="rounded bg-red-600 px-4 py-2 text-white hover:bg-red-500 transition-colors duration-200"
                          >
                            Sign Out
                          </button>
                        </div>
                      </li>
                    </ul>
                  </nav>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </Dialog>
      </Transition.Root>

      <div className="hidden lg:fixed lg:inset-y-0 lg:z-50 lg:flex lg:w-72 lg:flex-col">
        <div className="flex grow flex-col gap-y-5 overflow-y-auto bg-gradient-to-b from-gray-900 via-gray-800 to-gray-900 px-6 scrollbar-hide">
          <div className="flex h-16 shrink-0 items-center justify-center">
            <span className="text-2xl font-bold bg-gradient-to-r from-blue-400 to-cyan-300 bg-clip-text text-transparent">
              Genius Invoices
            </span>
          </div>
          <nav className="flex flex-1 flex-col">
            <ul role="list" className="flex flex-1 flex-col gap-y-7">
              {navigation.map((group) => (
                <li key={group.category}>
                  <div className="text-xs font-semibold leading-6 text-gray-400">{group.category}</div>
                  <ul role="list" className="-mx-2 mt-2 space-y-1">
                    {group.items.map((item) => (
                      <li key={item.name}>
                        <a
                          href={item.href}
                          onClick={(e) => {
                            if (item.bankItems) {
                              handleBankClick(e, item);
                            } else if (item.dropdownItems) {
                              handleDropdownClick(e, item.name);
                            } else {
                              handleSettingsClick(e, item.href);
                            }
                          }}
                          className={classNames(
                            item.current
                              ? "bg-gray-800 text-white border-l-4 border-blue-500 shadow-lg"
                              : "text-gray-400 hover:text-white hover:bg-gray-800",
                            "group flex gap-x-3 rounded-md p-2 text-sm leading-6 font-semibold transition-colors duration-200"
                          )}
                        >
                          <item.icon
                            className={classNames(
                              "h-6 w-6 shrink-0 transition-all duration-300 ease-in-out",
                              item.current
                                ? "text-white scale-110"
                                : item.iconColor,
                              "group-hover:scale-110 group-hover:rotate-3"
                            )}
                            aria-hidden="true"
                          />
                          <span className="flex-1">{item.name}</span>
                          {(item.dropdownItems || item.bankItems) && (
                            <ChevronDownIcon
                              className={classNames(
                                "h-5 w-5 transition-transform duration-200",
                                (openDropdown === item.name || (item.bankItems && openBankMenu)) ? "rotate-180" : ""
                              )}
                            />
                          )}
                        </a>
                        {item.bankItems && openBankMenu && (
                          <div className="mt-1 px-8">
                            <div className="mb-2">
                              <input
                                type="text"
                                value={bankSearchQuery}
                                onChange={(e) => setBankSearchQuery(e.target.value)}
                                placeholder="Search banks..."
                                className="w-full px-3 py-2 text-sm bg-gray-700 text-gray-200 rounded-md border border-gray-600 focus:outline-none focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                              />
                            </div>
                            <ul className="space-y-1 max-h-48 overflow-y-auto scrollbar-hide" style={{ msOverflowStyle: 'none', scrollbarWidth: 'none' }}>
                              {filterBanks(item.bankItems).map((bank) => (
                                <li key={bank.name}>
                                  <a
                                    href={bank.href}
                                    className="block rounded-md py-2 px-3 text-sm text-gray-400 hover:bg-gray-800 hover:text-white transition-colors duration-200"
                                  >
                                    {bank.name}
                                  </a>
                                </li>
                              ))}
                              {filterBanks(item.bankItems).length === 0 && (
                                <li className="py-2 px-3 text-sm text-gray-500 italic">
                                  No banks found
                                </li>
                              )}
                            </ul>
                          </div>
                        )}
                        {item.dropdownItems && openDropdown === item.name && (
                          <ul className="mt-1 space-y-1 px-8">
                            {item.dropdownItems.map((dropdownItem) => (
                              <li key={dropdownItem.name}>
                                <a
                                  href={dropdownItem.href}
                                  className="block rounded-md py-2 px-3 text-sm text-gray-400 hover:bg-gray-800 hover:text-white transition-colors duration-200"
                                >
                                  {dropdownItem.name}
                                </a>
                              </li>
                            ))}
                          </ul>
                        )}
                      </li>
                    ))}
                  </ul>
                </li>
              ))}

              <li className="-mx-6 mt-auto">
                <div className="flex items-center gap-x-4 px-6 py-3 text-sm font-semibold leading-6 text-white hover:bg-gray-800 transition-colors duration-200">
                  <div className="relative">
                    <img
                      className="h-10 w-10 rounded-full border-2 border-gray-600"
                      src={userData?.profileImageUrl || "https://res.cloudinary.com/dfilgqymt/image/upload/f_auto,q_auto/360_F_215844325_ttX9YiIIyeaR7Ne6EaLLjMAmy4GvPC69_bf0abd"}
                      alt={userData?.name || "User"}
                    />
                    {userData?.isSubAccount && (
                      <span className="absolute -top-1 -right-1 bg-indigo-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                        S
                      </span>
                    )}
                  </div>
                  <div className="flex-1">
                    <span>{userData?.name || "Anonymous User"}</span>
                    {userData?.isSubAccount && (
                      <span className="block text-xs text-gray-400">Sub-Account</span>
                    )}
                  </div>
                  <button
                    onClick={handleSignOut}
                    className="rounded bg-red-600 px-4 py-2 text-white hover:bg-red-500 transition-colors duration-200"
                  >
                    Sign Out
                  </button>
                </div>
              </li>
            </ul>
          </nav>
        </div>
      </div>

      <div className="sticky top-0 z-40 flex items-center gap-x-6 bg-gray-900 px-4 py-4 shadow-sm sm:px-6 lg:hidden">
        <button type="button" className="-m-2.5 p-2.5 text-gray-400 lg:hidden" onClick={() => setSidebarOpen(true)}>
          <span className="sr-only">Open sidebar</span>
          <Bars3Icon className="h-6 w-6" aria-hidden="true" />
        </button>
        <div className="flex-1 text-sm font-semibold leading-6 text-white">Dashboard</div>
        <div className="relative">
          <img
            className="h-8 w-8 rounded-full bg-gray-800"
            src={userData?.profileImageUrl || "https://res.cloudinary.com/dfilgqymt/image/upload/f_auto,q_auto/360_F_215844325_ttX9YiIIyeaR7Ne6EaLLjMAmy4GvPC69_bf0abd"}
            alt={userData?.name || "User"}
          />
          {userData?.isSubAccount && (
            <span className="absolute -top-1 -right-1 bg-indigo-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
              S
            </span>
          )}
        </div>
      </div>
      <main className="py-10 lg:pl-72 overflow-x-hidden">
        <div className="px-4 sm:px-6 lg:px-8 overflow-x-hidden">
          {pathname === "/DashBoard" && (
            <>
              <div className="bg-gradient-to-r from-indigo-600 to-blue-500 text-white rounded-2xl shadow-lg mb-8 overflow-hidden">
                <div className="relative z-10 p-8">
                  <div className="flex flex-col md:flex-row md:items-center justify-between">
                    <div className="space-y-3">
                      {/* Dynamic greeting based on time of day */}
                      <div className="flex items-center space-x-2">
                        <span className="inline-flex items-center justify-center bg-white/20 rounded-full p-1.5">
                          {new Date().getHours() < 12 ? (
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-yellow-300" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clipRule="evenodd" />
                            </svg>
                          ) : new Date().getHours() < 18 ? (
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-orange-300" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M10 2a8 8 0 100 16 8 8 0 000-16zm0 14a6 6 0 110-12 6 6 0 010 12z" clipRule="evenodd" />
                            </svg>
                          ) : (
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-indigo-200" viewBox="0 0 20 20" fill="currentColor">
                              <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z" />
                            </svg>
                          )}
                        </span>
                        <span className="text-sm font-medium text-indigo-100">
                          {new Date().getHours() < 12
                            ? "Good morning"
                            : new Date().getHours() < 18
                            ? "Good afternoon"
                            : "Good evening"}
                        </span>
                      </div>

                      <h1 className="text-3xl md:text-4xl font-bold tracking-tight">
                        Welcome back, <span className="text-indigo-200">{userData?.name || "User"}</span>
                      </h1>

                      <p className="text-indigo-100 max-w-2xl">
                        {new Date().toLocaleDateString('en-US', {
                          weekday: 'long',
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })}
                      </p>
                    </div>

                    <div className="mt-4 md:mt-0 flex space-x-2">
                      <a href="/Invoices/Achat" className="inline-flex items-center px-4 py-2 bg-white/10 hover:bg-white/20 rounded-lg transition-colors duration-150 text-sm font-medium">
                        <DocumentIcon className="h-5 w-5 mr-2" />
                        New Invoice
                      </a>
                      <a href="/ClientList" className="inline-flex items-center px-4 py-2 bg-white/10 hover:bg-white/20 rounded-lg transition-colors duration-150 text-sm font-medium">
                        <UserGroupIcon className="h-5 w-5 mr-2" />
                        Add Client
                      </a>
                    </div>
                  </div>
                </div>

                {/* Decorative elements */}
                <div className="absolute top-0 right-0 opacity-50 overflow-hidden">
                  <svg width="300" height="300" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
                    <path fill="rgba(255, 255, 255, 0.1)" d="M47.4,-51.2C59.2,-35.3,65.1,-17.6,64.4,-0.7C63.8,16.2,56.5,32.4,44.7,44.1C32.8,55.8,16.4,63,0.8,62.2C-14.8,61.5,-29.6,52.7,-43.9,41C-58.3,29.3,-72.2,14.7,-72.3,-0.1C-72.4,-14.8,-58.7,-29.7,-44.3,-45.5C-29.9,-61.4,-15,-78.1,1.3,-79.5C17.6,-80.8,35.2,-67,47.4,-51.2Z" transform="translate(100 100)" />
                  </svg>
                </div>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div className="bg-white rounded-xl shadow-md p-6 hover:shadow-lg transition-shadow duration-300">
                  <h2 className="text-lg font-semibold text-gray-800 mb-4">Quick Actions</h2>
                  <div className="space-y-4">
                    <a
                      href="/Invoices/Achat"
                      className="flex items-center p-3 bg-blue-50 text-blue-700 rounded-lg hover:bg-blue-100 transition-colors duration-200"
                    >
                      <ShoppingCartIcon className="h-5 w-5 mr-3" />
                      Create Buy Invoice
                    </a>
                    <a
                      href="/Invoices/Vente"
                      className="flex items-center p-3 bg-green-50 text-green-700 rounded-lg hover:bg-green-100 transition-colors duration-200"
                    >
                      <CurrencyDollarIcon className="h-5 w-5 mr-3" />
                      Create Sell Invoice
                    </a>
                    <a
                      href="/Invoices/Caisse"
                      className="flex items-center p-3 bg-yellow-50 text-yellow-700 rounded-lg hover:bg-yellow-100 transition-colors duration-200"
                    >
                      <CalculatorIcon className="h-5 w-5 mr-3" />
                      Create Register Invoice
                    </a>
                    <a
                      href="/Invoices/History"
                      className="flex items-center p-3 bg-purple-50 text-purple-700 rounded-lg hover:bg-purple-100 transition-colors duration-200"
                    >
                      <ClockIcon className="h-5 w-5 mr-3" />
                      View Invoice History
                    </a>
                  </div>
                </div>

                <div className="lg:col-span-2 bg-white rounded-xl shadow-md p-6 hover:shadow-lg transition-shadow duration-300">
                  <h2 className="text-lg font-semibold text-gray-800 mb-4">Recent Invoices</h2>
                  <div className="overflow-hidden">
                    <table className="min-w-full divide-y divide-gray-200">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice #</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {loadingInvoices ? (
                          <tr>
                            <td colSpan={5} className="px-6 py-4 text-center">
                              <div className="flex justify-center items-center space-x-2">
                                <ArrowPathIcon className="h-4 w-4 animate-spin text-blue-500" />
                                <span className="text-sm text-gray-500">Loading invoices...</span>
                              </div>
                            </td>
                          </tr>
                        ) : recentInvoices.length > 0 ? (
                          recentInvoices.map((invoice) => (
                            <tr key={invoice.id}>
                              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{invoice.invoiceNumber}</td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{invoice.type}</td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{formatDate(invoice.date)}</td>
                              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{formatAmount(invoice.amount)} DT</td>
                              <td className="px-6 py-4 whitespace-nowrap">
                              </td>
                            </tr>
                          ))
                        ) : (
                          <tr>
                            <td colSpan={5} className="px-6 py-4 text-center text-sm text-gray-500">
                              No recent invoices found
                            </td>
                          </tr>
                        )}
                      </tbody>
                    </table>
                  </div>
                  <div className="mt-4 text-right">
                    <a href="/Invoices/History" className="text-blue-600 hover:text-blue-800 text-sm font-medium">
                      View All Invoices →
                    </a>
                  </div>
                </div>
              </div>
            </>
          )}
        </div>
      </main>
    </div>
  );
}
